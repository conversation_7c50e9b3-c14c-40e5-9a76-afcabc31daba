import type { Metadata } from "next";

export const metadata: Metadata = {
  title: "ChaiCourseGPT - Your AI Coding Instructor",
  description: "Learn coding faster with ChaiCourseGPT - an AI assistant powered by <PERSON><PERSON>'s teaching style. Get instant help, code examples, and personalized explanations for your course content.",
  keywords: "AI coding assistant, programming help, <PERSON><PERSON>, coding tutor, learn programming, JavaScript, React, Node.js",
  authors: [{ name: "ChaiCourseGPT Team" }],
  openGraph: {
    title: "ChaiCourseGPT - Your AI Coding Instructor",
    description: "Learn coding faster with ChaiCourseGPT - an AI assistant powered by <PERSON><PERSON>'s teaching style.",
    type: "website",
    locale: "en_US",
  },
  twitter: {
    card: "summary_large_image",
    title: "ChaiCourseGPT - Your AI Coding Instructor",
    description: "Learn coding faster with ChaiCourseGPT - an AI assistant powered by <PERSON><PERSON>'s teaching style.",
  },
  robots: {
    index: true,
    follow: true,
  },
};

export default function LandingLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}
