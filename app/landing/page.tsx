"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { CardEnhanced } from "@/components/ui/card-enhanced";
import { AvatarEnhanced } from "@/components/ui/avatar-enhanced";
import { Badge } from "@/components/ui/badge";
import { utils } from "@/lib/design-system";
import Image from "next/image";
import Link from "next/link";
import {
  ArrowRight,
  BookOpen,
  Code2,
  Zap,
  Star,
  Play,
  CheckCircle,
  MessageCircle,
  Brain,
  Sparkles
} from "lucide-react";

const LandingPage = () => {
  return (
    <div 
      className="min-h-screen"
      style={{ backgroundColor: "#0a0a0a" }}
    >
      {/* Header */}
      <header className="border-b border-gray-800 px-4 sm:px-6 py-4 backdrop-blur-sm">
        <div className="mx-auto max-w-7xl flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="relative">
              <div className="flex h-10 w-10 items-center justify-center rounded-xl overflow-hidden shadow-lg">
                <Image
                  src="/chai.webp"
                  width={40}
                  height={40}
                  alt="ChaiCourseGPT logo"
                  className="object-cover"
                />
              </div>
              <div className="absolute -bottom-1 -right-1 h-3 w-3 rounded-full bg-green-500 border-2 border-gray-900"></div>
            </div>
            <div>
              <h1 className="text-xl font-bold text-white">ChaiCourseGPT</h1>
              <p className="text-xs text-orange-400">v1.0</p>
            </div>
          </div>
          
          <nav className="hidden md:flex items-center gap-6">
            <a href="#features" className="text-gray-300 hover:text-orange-400 transition-colors">Features</a>
            <a href="#how-it-works" className="text-gray-300 hover:text-orange-400 transition-colors">How it Works</a>
            <a href="#testimonials" className="text-gray-300 hover:text-orange-400 transition-colors">Reviews</a>
          </nav>

          <Link href="/chat">
            <Button className="bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700">
              Get Started
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </Link>
        </div>
      </header>

      {/* Hero Section */}
      <section className="px-4 sm:px-6 py-16 sm:py-24">
        <div className="mx-auto max-w-7xl text-center">
          <div className="relative mx-auto mb-8 w-32 h-32">
            <div className="absolute inset-0 bg-gradient-to-r from-orange-500/20 to-orange-600/20 rounded-full blur-xl animate-pulse"></div>
            <div className="relative flex h-full w-full items-center justify-center rounded-2xl overflow-hidden shadow-2xl">
              <Image
                src="/chai.webp"
                width={128}
                height={128}
                alt="ChaiCourseGPT - Your AI Coding Instructor"
                className="object-cover"
              />
            </div>
          </div>

          <div className="space-y-6 mb-12">
            <h1 className="text-4xl sm:text-6xl font-bold text-white tracking-tight">
              Namaste! Welcome to{" "}
              <span className="bg-gradient-to-r from-orange-400 to-orange-600 bg-clip-text text-transparent">
                ChaiCourseGPT
              </span>
            </h1>
            
            <p className="text-xl sm:text-2xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
              <span className="text-orange-400 font-semibold">Seedhi si baat hai</span> - 
              Your AI coding instructor powered by Hitesh Choudhary's teaching style. 
              Learn faster, code better, and get instant help with your course content.
            </p>
          </div>

          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-16">
            <Link href="/chat">
              <Button 
                size="lg" 
                className="bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-lg px-8 py-4 h-auto"
              >
                <Play className="mr-2 h-5 w-5" />
                Start Learning Now
              </Button>
            </Link>
            
            <Button 
              variant="outline" 
              size="lg" 
              className="border-gray-600 text-gray-300 hover:bg-gray-800 text-lg px-8 py-4 h-auto"
            >
              <BookOpen className="mr-2 h-5 w-5" />
              Watch Demo
            </Button>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto">
            <div className="text-center">
              <div className="text-3xl font-bold text-orange-400">24/7</div>
              <div className="text-gray-400">Available</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-orange-400">1000+</div>
              <div className="text-gray-400">Topics Covered</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-orange-400">Instant</div>
              <div className="text-gray-400">Responses</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-orange-400">Smart</div>
              <div className="text-gray-400">AI Assistant</div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="px-4 sm:px-6 py-16 bg-gradient-to-b from-gray-900/50 to-transparent">
        <div className="mx-auto max-w-7xl">
          <div className="text-center mb-16">
            <h2 className="text-3xl sm:text-4xl font-bold text-white mb-4">
              Why Choose ChaiCourseGPT?
            </h2>
            <p className="text-xl text-gray-300 max-w-2xl mx-auto">
              Haan ji! Here's what makes our AI assistant special for your coding journey
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <CardEnhanced variant="default" padding="lg" className={utils.hover("scale-105")}>
              <div className="text-center space-y-4">
                <div className="mx-auto w-16 h-16 bg-gradient-to-br from-orange-500 to-orange-600 rounded-2xl flex items-center justify-center">
                  <Brain className="h-8 w-8 text-white" />
                </div>
                <h3 className="text-xl font-semibold text-white">Smart AI Assistant</h3>
                <p className="text-gray-400">
                  Powered by advanced AI that understands your course content and provides personalized explanations in Hitesh's teaching style.
                </p>
              </div>
            </CardEnhanced>

            <CardEnhanced variant="default" padding="lg" className={utils.hover("scale-105")}>
              <div className="text-center space-y-4">
                <div className="mx-auto w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center">
                  <Code2 className="h-8 w-8 text-white" />
                </div>
                <h3 className="text-xl font-semibold text-white">Code Help & Debugging</h3>
                <p className="text-gray-400">
                  Get instant help with coding problems, debug issues, and understand complex concepts with practical examples.
                </p>
              </div>
            </CardEnhanced>

            <CardEnhanced variant="default" padding="lg" className={utils.hover("scale-105")}>
              <div className="text-center space-y-4">
                <div className="mx-auto w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-2xl flex items-center justify-center">
                  <Zap className="h-8 w-8 text-white" />
                </div>
                <h3 className="text-xl font-semibold text-white">Instant Responses</h3>
                <p className="text-gray-400">
                  No waiting around! Get immediate answers to your questions with precise timestamps and source references.
                </p>
              </div>
            </CardEnhanced>
          </div>
        </div>
      </section>

      {/* How it Works Section */}
      <section id="how-it-works" className="px-4 sm:px-6 py-16">
        <div className="mx-auto max-w-7xl">
          <div className="text-center mb-16">
            <h2 className="text-3xl sm:text-4xl font-bold text-white mb-4">
              How ChaiCourseGPT Works
            </h2>
            <p className="text-xl text-gray-300 max-w-2xl mx-auto">
              Simple, effective, and designed for your learning success
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center space-y-4">
              <div className="mx-auto w-20 h-20 bg-gradient-to-br from-orange-500 to-orange-600 rounded-full flex items-center justify-center text-2xl font-bold text-white">
                1
              </div>
              <h3 className="text-xl font-semibold text-white">Ask Your Question</h3>
              <p className="text-gray-400">
                Type any coding question, concept doubt, or request for explanation. Our AI understands context and provides relevant answers.
              </p>
            </div>

            <div className="text-center space-y-4">
              <div className="mx-auto w-20 h-20 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center text-2xl font-bold text-white">
                2
              </div>
              <h3 className="text-xl font-semibold text-white">Get Smart Answers</h3>
              <p className="text-gray-400">
                Receive detailed explanations in Hitesh's teaching style with code examples, best practices, and practical insights.
              </p>
            </div>

            <div className="text-center space-y-4">
              <div className="mx-auto w-20 h-20 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center text-2xl font-bold text-white">
                3
              </div>
              <h3 className="text-xl font-semibold text-white">Learn & Apply</h3>
              <p className="text-gray-400">
                Apply the knowledge immediately with source references, timestamps, and follow-up questions for deeper understanding.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section id="testimonials" className="px-4 sm:px-6 py-16 bg-gradient-to-b from-gray-900/50 to-transparent">
        <div className="mx-auto max-w-7xl">
          <div className="text-center mb-16">
            <h2 className="text-3xl sm:text-4xl font-bold text-white mb-4">
              What Students Say
            </h2>
            <p className="text-xl text-gray-300 max-w-2xl mx-auto">
              Real feedback from learners who've experienced the ChaiCourseGPT difference
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <CardEnhanced variant="default" padding="lg">
              <div className="space-y-4">
                <div className="flex items-center gap-1">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="h-4 w-4 fill-orange-400 text-orange-400" />
                  ))}
                </div>
                <p className="text-gray-300">
                  "Haan ji, this is exactly what I needed! The explanations are so clear and the Hinglish style makes everything easy to understand."
                </p>
                <div className="flex items-center gap-3">
                  <AvatarEnhanced
                    src="/user.svg"
                    alt="Student"
                    size="sm"
                    className="bg-gradient-to-br from-blue-500 to-blue-600"
                  />
                  <div>
                    <div className="font-semibold text-white">Priya Sharma</div>
                    <div className="text-sm text-gray-400">React Developer</div>
                  </div>
                </div>
              </div>
            </CardEnhanced>

            <CardEnhanced variant="default" padding="lg">
              <div className="space-y-4">
                <div className="flex items-center gap-1">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="h-4 w-4 fill-orange-400 text-orange-400" />
                  ))}
                </div>
                <p className="text-gray-300">
                  "The instant responses and code examples saved me hours of debugging. It's like having Hitesh sir available 24/7!"
                </p>
                <div className="flex items-center gap-3">
                  <AvatarEnhanced
                    src="/user.svg"
                    alt="Student"
                    size="sm"
                    className="bg-gradient-to-br from-green-500 to-green-600"
                  />
                  <div>
                    <div className="font-semibold text-white">Rahul Kumar</div>
                    <div className="text-sm text-gray-400">Full Stack Developer</div>
                  </div>
                </div>
              </div>
            </CardEnhanced>

            <CardEnhanced variant="default" padding="lg">
              <div className="space-y-4">
                <div className="flex items-center gap-1">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="h-4 w-4 fill-orange-400 text-orange-400" />
                  ))}
                </div>
                <p className="text-gray-300">
                  "Perfect for quick doubts and concept clarification. The source references help me find exact video timestamps!"
                </p>
                <div className="flex items-center gap-3">
                  <AvatarEnhanced
                    src="/user.svg"
                    alt="Student"
                    size="sm"
                    className="bg-gradient-to-br from-purple-500 to-purple-600"
                  />
                  <div>
                    <div className="font-semibold text-white">Anita Patel</div>
                    <div className="text-sm text-gray-400">Frontend Developer</div>
                  </div>
                </div>
              </div>
            </CardEnhanced>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="px-4 sm:px-6 py-16">
        <div className="mx-auto max-w-4xl text-center">
          <CardEnhanced variant="elevated" padding="lg" className="bg-gradient-to-r from-orange-500/10 to-orange-600/10 border-orange-500/20 p-8">
            <div className="space-y-6">
              <div className="mx-auto w-16 h-16 bg-gradient-to-br from-orange-500 to-orange-600 rounded-2xl flex items-center justify-center">
                <Sparkles className="h-8 w-8 text-white" />
              </div>

              <h2 className="text-3xl sm:text-4xl font-bold text-white">
                Ready to Accelerate Your Learning?
              </h2>

              <p className="text-xl text-gray-300 max-w-2xl mx-auto">
                Join thousands of students who are already learning faster with ChaiCourseGPT.
                <span className="text-orange-400 font-semibold"> Seedhi si baat hai</span> -
                it's time to level up your coding skills!
              </p>

              <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
                <Link href="/chat">
                  <Button
                    size="lg"
                    className="bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-lg px-8 py-4 h-auto"
                  >
                    <MessageCircle className="mr-2 h-5 w-5" />
                    Start Chatting Now
                  </Button>
                </Link>

                <div className="flex items-center gap-2 text-gray-400">
                  <CheckCircle className="h-5 w-5 text-green-400" />
                  <span>Free to use • No signup required</span>
                </div>
              </div>
            </div>
          </CardEnhanced>
        </div>
      </section>

      {/* Footer */}
      <footer className="border-t border-gray-800 px-4 sm:px-6 py-8">
        <div className="mx-auto max-w-7xl">
          <div className="flex flex-col md:flex-row items-center justify-between gap-4">
            <div className="flex items-center gap-3">
              <div className="flex h-8 w-8 items-center justify-center rounded-lg overflow-hidden">
                <Image
                  src="/chai.webp"
                  width={32}
                  height={32}
                  alt="ChaiCourseGPT logo"
                  className="object-cover"
                />
              </div>
              <div>
                <div className="font-semibold text-white">ChaiCourseGPT</div>
                <div className="text-xs text-gray-400">Powered by Hitesh Choudhary</div>
              </div>
            </div>

            <div className="text-center text-gray-400">
              <p>&copy; 2024 ChaiCourseGPT. Made with ❤️ for the coding community.</p>
            </div>

            <div className="flex items-center gap-4">
              <Badge variant="outline" className="border-green-500/50 text-green-400">
                <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                Online
              </Badge>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default LandingPage;
